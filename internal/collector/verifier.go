package collector

import (
	"context"
	"encoding/json"
	"fmt"
	"net"
	"net/http"
	"net/url"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"proxyFlow/internal/concurrent"
	"proxyFlow/internal/models"
	"proxyFlow/pkg/config"

	"github.com/pterm/pterm"
	"github.com/sirupsen/logrus"
)

// VerificationResult 验证结果
type VerificationResult struct {
	Proxy          *CollectedProxy       `json:"proxy"`
	Success        bool                  `json:"success"`
	ResponseTime   int64                 `json:"response_time"` // 毫秒
	Error          string                `json:"error,omitempty"`
	RealIP         string                `json:"real_ip,omitempty"`
	AnonymityLevel models.AnonymityLevel `json:"anonymity_level,omitempty"`
}

// IPResponse httpbin.org/ip 响应结构
type IPResponse struct {
	Origin string `json:"origin"`
}

// VerificationProgress 验证进度记录
type VerificationProgress struct {
	TotalProxies     int               `json:"total_proxies"`
	ProcessedProxies int               `json:"processed_proxies"`
	ValidNumber      int               `json:"valid_number"`
	ValidProxies     []*CollectedProxy `json:"valid_proxies"`
	ProcessedHosts   map[string]bool   `json:"processed_hosts"` // 记录已处理的代理
	StartTime        time.Time         `json:"start_time"`
	LastUpdateTime   time.Time         `json:"last_update_time"`
	BatchSize        int               `json:"batch_size"`
	Version          string            `json:"version"`
}

// VerificationProgressDisplay 验证进度显示器
type VerificationProgressDisplay struct {
	progressbar *pterm.ProgressbarPrinter
	config      *config.CollectorConfig
	logger      *logrus.Logger

	// 统计信息
	startTime    time.Time
	totalProxies int64
	processed    int64
	successful   int64
	failed       int64

	// 当前会话统计（用于正确计算速度）
	sessionStartProcessed int64   // 会话开始时的已处理数量
	currentSessionSpeed   float64 // 当前会话的真实速度

	// 控制通道
	updateCh chan ProgressUpdate
	stopCh   chan struct{}
	mu       sync.RWMutex
}

// ProgressUpdate 进度更新数据
type ProgressUpdate struct {
	Type        string // "batch_start", "batch_complete", "proxy_complete"
	BatchInfo   *BatchInfo
	ProxyInfo   *ProxyInfo
	CurrentHost string
}

type ProgressUpdateMeta struct {
	processed  int64
	successful int64
	failed     int64
}

// BatchInfo 批次信息
type BatchInfo struct {
	BatchIndex int
	BatchSize  int
	TotalBatch int
}

// ProxyInfo 代理信息
type ProxyInfo struct {
	Host    string
	Port    int
	Success bool
	Error   string
}

// 创建验证进度显示器
func NewVerificationProgressDisplay(config *config.CollectorConfig, logger *logrus.Logger, totalProxies int, initialProcessed int64) *VerificationProgressDisplay {
	if !config.EnableProgressDisplay {
		return nil
	}

	// 初始化PTerm
	pterm.DisableDebugMessages()

	// 创建美丽的进度条
	progressbar, _ := pterm.DefaultProgressbar.WithTotal(totalProxies).WithTitle("🔍 代理验证进度").Start()

	display := &VerificationProgressDisplay{
		progressbar:           progressbar,
		config:                config,
		logger:                logger,
		startTime:             time.Now(),
		totalProxies:          int64(totalProxies),
		sessionStartProcessed: initialProcessed, // 设置会话开始时的已处理数量
		updateCh:              make(chan ProgressUpdate, 100),
		stopCh:                make(chan struct{}),
	}

	// 启动更新协程
	go display.updateLoop()

	return display
}

// updateLoop 更新循环（优化版）
func (vpd *VerificationProgressDisplay) updateLoop() {
	// 使用更合适的更新间隔，避免过于频繁的更新
	updateTicker := time.NewTicker(500 * time.Millisecond) // 改为500ms
	statsTicker := time.NewTicker(2 * time.Second)         // 统计更新间隔更长
	defer func() {
		updateTicker.Stop()
		statsTicker.Stop()
	}()

	for {
		select {
		case update := <-vpd.updateCh:
			vpd.handleUpdate(update)

		case <-updateTicker.C:
			// 更温和的定时更新，只在有变化时才更新
			if time.Since(vpd.startTime) > time.Second {
				vpd.updateStatsIfNeeded()
			}

		case <-statsTicker.C:
			// 定期输出详细统计
			vpd.logDetailedStats()

		case <-vpd.stopCh:
			return
		}
	}
}

// handleUpdate 处理更新
func (vpd *VerificationProgressDisplay) handleUpdate(update ProgressUpdate) {
	vpd.mu.Lock()
	defer vpd.mu.Unlock()

	switch update.Type {
	case "proxy_complete":
		// 更新统计计数
		vpd.processed++
		if update.ProxyInfo != nil {
			if update.ProxyInfo.Success {
				vpd.successful++
			} else {
				vpd.failed++
			}
		}

		// 更新进度条
		vpd.progressbar.Increment()

	case "batch_start", "batch_complete":
		// 忽略批次事件，只关注总体进度
	}
}

// updateStatsIfNeeded 按需更新统计
func (vpd *VerificationProgressDisplay) updateStatsIfNeeded() {
	vpd.mu.RLock()
	defer vpd.mu.RUnlock()

	// 计算速度（代理/秒）
	elapsed := time.Since(vpd.startTime).Seconds()
	if elapsed > 0 {
		speed := float64(vpd.processed) / elapsed
		// 只有在速度变化较大时才记录（避免日志垃圾）
		_ = speed // 可以添加更细致的速度计算逻辑
	}
}

// logDetailedStats 记录详细统计（调试用）
func (vpd *VerificationProgressDisplay) logDetailedStats() {
	vpd.mu.RLock()
	defer vpd.mu.RUnlock()

	elapsed := time.Since(vpd.startTime)
	if vpd.processed > 0 && elapsed.Seconds() > 1 {
		speed := float64(vpd.processed) / elapsed.Seconds()
		successRate := float64(vpd.successful) / float64(vpd.processed) * 100
		progressPct := float64(vpd.processed) / float64(vpd.totalProxies) * 100

		vpd.logger.WithFields(logrus.Fields{
			"processed":    vpd.processed,
			"total":        vpd.totalProxies,
			"successful":   vpd.successful,
			"failed":       vpd.failed,
			"progress_pct": fmt.Sprintf("%.1f%%", progressPct),
			"success_rate": fmt.Sprintf("%.1f%%", successRate),
			"speed":        fmt.Sprintf("%.0f/s", speed),
			"elapsed":      elapsed.Round(time.Second),
			"eta":          vpd.calculateETA(speed),
		}).Debug("Verification progress stats")
	}
}

// calculateETA 计算预计完成时间
func (vpd *VerificationProgressDisplay) calculateETA(speed float64) string {
	if speed <= 0 {
		return "unknown"
	}
	remaining := vpd.totalProxies - vpd.processed
	if remaining <= 0 {
		return "0s"
	}
	etaSeconds := float64(remaining) / speed
	return time.Duration(etaSeconds * float64(time.Second)).Round(time.Second).String()
}

// updateStats 更新统计信息（保留原方法兼容性）
// 优化的updateStats方法（保留兼容性）
func (vpd *VerificationProgressDisplay) updateStats() {
	// 简化处理，调用更优化的方法
	vpd.updateStatsIfNeeded()
}

// Update 发送更新
func (vpd *VerificationProgressDisplay) Update(update ProgressUpdate) {
	if vpd == nil {
		return
	}

	select {
	case vpd.updateCh <- update:
	default:
		// 如果通道满了，跳过这次更新
	}
}

// UpdateMeta 更新进度条显示（改进版）
func (vpd *VerificationProgressDisplay) UpdateMeta(update ProgressUpdateMeta) {
	if vpd == nil {
		return
	}
	vpd.mu.Lock()
	defer vpd.mu.Unlock()

	// 更新内部状态
	oldProcessed := vpd.processed
	vpd.processed = update.processed
	vpd.failed = update.failed
	vpd.successful = update.successful

	// 计算当前会话的真实验证速度
	elapsed := time.Since(vpd.startTime)
	currentSessionProcessed := vpd.processed - vpd.sessionStartProcessed

	var speed float64
	if elapsed.Seconds() > 0 && currentSessionProcessed > 0 {
		// 速度 = 当前会话实际验证的代理数 / 当前会话时间
		speed = float64(currentSessionProcessed) / elapsed.Seconds()
		vpd.currentSessionSpeed = speed
	} else {
		// 如果没有新的验证，使用上次记录的速度
		speed = vpd.currentSessionSpeed
	}

	var successRate float64
	if vpd.processed > 0 {
		successRate = float64(vpd.successful) / float64(vpd.processed) * 100
	}

	// 动态更新标题
	title := fmt.Sprintf("🔍 代理验证 | ✓%d ✗%d | 成功率%.1f%% | 速度%.0f/s",
		vpd.successful, vpd.failed, successRate, speed)

	vpd.progressbar.UpdateTitle(title)

	// 安全更新进度条
	currentProgress := int(vpd.processed)
	if currentProgress > vpd.progressbar.Total {
		currentProgress = vpd.progressbar.Total
	}

	// 计算需要增加的进度（确保不会倒退）
	toAdd := currentProgress - vpd.progressbar.Current
	if toAdd > 0 {
		vpd.progressbar.Add(toAdd)
	} else if currentProgress != vpd.progressbar.Current {
		// 如果进度不同步，重新设置进度条
		diff := currentProgress - vpd.progressbar.Current
		if diff < 0 {
			// 避免负值，重置进度条
			vpd.progressbar = vpd.progressbar.WithCurrent(currentProgress)
		}
	}

	// 记录处理速度变化（用于调试）
	if update.processed > oldProcessed && elapsed.Seconds() > 1 {
		vpd.logger.WithFields(logrus.Fields{
			"processed":    vpd.processed,
			"successful":   vpd.successful,
			"failed":       vpd.failed,
			"progress_pct": fmt.Sprintf("%.1f%%", float64(vpd.processed)/float64(vpd.totalProxies)*100),
			"speed":        fmt.Sprintf("%.0f/s", speed),
		}).Debug("Progress display updated")
	}
}

// Stop 停止进度显示（改进版）
func (vpd *VerificationProgressDisplay) Stop() {
	if vpd == nil {
		return
	}

	// 使用sync.Once确保只停止一次
	var once sync.Once
	once.Do(func() {
		// 安全关闭停止通道
		select {
		case <-vpd.stopCh:
			// 已经关闭
		default:
			close(vpd.stopCh)
		}

		// 停止PTerm进度条
		if vpd.progressbar != nil {
			// 确保进度条显示100%完成
			vpd.mu.Lock()
			finalProgress := int(vpd.processed)
			if finalProgress < vpd.progressbar.Total {
				// 补齐到100%
				remaining := vpd.progressbar.Total - vpd.progressbar.Current
				if remaining > 0 {
					vpd.progressbar.Add(remaining)
				}
			}
			vpd.mu.Unlock()

			// 停止进度条
			vpd.progressbar.Stop()

			// 显示最终统计
			vpd.mu.RLock()
			elapsed := time.Since(vpd.startTime)
			speed := float64(vpd.processed) / elapsed.Seconds()
			var successRate float64
			if vpd.processed > 0 {
				successRate = float64(vpd.successful) / float64(vpd.processed) * 100
			}
			vpd.mu.RUnlock()

			// 美丽的结束信息
			if vpd.processed > 0 {
				pterm.Success.Printf("验证完成! 处理: %d | ✓成功: %d | ✗失败: %d | 成功率: %.2f%% | 用时: %s | 平均速度: %.0f/s\n",
					vpd.processed, vpd.successful, vpd.failed, successRate, elapsed.Round(time.Second), speed)
			} else {
				pterm.Info.Println("验证完成（无任务处理）")
			}
		}
	})
}

// GetStats 获取当前统计信息
func (vpd *VerificationProgressDisplay) GetStats() (processed, successful, failed int64, elapsed time.Duration) {
	if vpd == nil {
		return 0, 0, 0, 0
	}

	vpd.mu.RLock()
	defer vpd.mu.RUnlock()

	return vpd.processed, vpd.successful, vpd.failed, time.Since(vpd.startTime)
}

// getVerificationProgressPath 获取验证进度文件路径
func (c *ProxyCollector) getVerificationProgressPath() string {
	progressDir := "freeProxy"
	if err := os.MkdirAll(progressDir, 0755); err != nil {
		c.logger.WithError(err).Warn("Failed to create progress directory, using current directory")
		progressDir = "."
	}
	return filepath.Join(progressDir, "verification_progress.json")
}

// 加载验证进度
func (c *ProxyCollector) loadVerificationProgress() (*VerificationProgress, error) {
	progressFile := c.getVerificationProgressPath()

	// 检查文件是否存在
	if _, err := os.Stat(progressFile); os.IsNotExist(err) {
		return nil, nil // 进度文件不存在
	}

	// 读取进度文件
	data, err := os.ReadFile(progressFile)
	if err != nil {
		return nil, fmt.Errorf("failed to read progress file: %w", err)
	}

	// 解析进度数据
	var progress VerificationProgress
	if err := json.Unmarshal(data, &progress); err != nil {
		return nil, fmt.Errorf("failed to unmarshal progress data: %w", err)
	}

	// 检查验证开始时间，如果超过1天则认为过期
	if time.Since(progress.StartTime) > 24*time.Hour {
		c.logger.WithFields(logrus.Fields{
			"start_time": progress.StartTime,
			"elapsed":    time.Since(progress.StartTime),
		}).Info("Verification progress expired (>24h), starting fresh")

		// 清除过期的进度文件内容
		if err := c.clearVerificationProgress(); err != nil {
			c.logger.WithError(err).Warn("Failed to clear expired progress file")
		}

		return nil, nil // 返回nil表示需要重新开始
	}

	return &progress, nil
}

// 保存验证进度
func (c *ProxyCollector) saveVerificationProgress(progress *VerificationProgress) error {
	progressFile := c.getVerificationProgressPath()

	progress.LastUpdateTime = time.Now()
	progress.ValidNumber = len(progress.ValidProxies)

	// 序列化进度数据
	data, err := json.MarshalIndent(progress, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal progress data: %w", err)
	}

	// 写入进度文件
	if err := os.WriteFile(progressFile, data, 0644); err != nil {
		return fmt.Errorf("failed to write progress file: %w", err)
	}

	return nil
}

// clearVerificationProgress 清除验证进度文件内容（仅在完全完成时使用）
func (c *ProxyCollector) clearVerificationProgress() error {
	progressFile := c.getVerificationProgressPath()
	if err := os.Remove(progressFile); err != nil && !os.IsNotExist(err) {
		return fmt.Errorf("failed to remove progress file: %w", err)
	}
	progress := VerificationProgress{}
	c.saveVerificationProgress(&progress)
	c.logger.WithField("progress_file", progressFile).Info("Verification progress was reset!")
	return nil
}

// 验证代理列表 (使用统一缓存系统)
func (c *ProxyCollector) verifyProxies(ctx context.Context, proxies []*CollectedProxy) ([]*CollectedProxy, error) {
	c.logger.WithField("total_proxies", len(proxies)).Info("Starting high-performance proxy verification with unified cache")

	// 初始化统一缓存系统
	var unifiedCache *Cache
	if c.config.CacheEnabled {
		cachePath := c.config.CacheFilePath
		if cachePath == "" {
			cachePath = "freeProxy/unified_proxy_cache.json" // 默认路径
		}

		unifiedCache = NewCache(
			cachePath,
			c.config.CacheValidDuration, // 短期缓存：30分钟
			24*time.Hour,                // 进度缓存：24小时
			c.logger,
		)
		defer func() {
			// 确保在函数结束时保存最终状态
			if err := unifiedCache.Save(); err != nil {
				c.logger.WithError(err).Error("Failed to save unified cache")
			} else {
				c.logger.WithFields(logrus.Fields{
					"total_entries": unifiedCache.GetSize(),
					"session_id":    unifiedCache.GetSessionProgress().SessionID,
				}).Info("Final unified cache saved successfully")
			}
		}()
	}

	// 从统一缓存中恢复进度和有效代理
	var validProxies []*CollectedProxy
	var toVerify []*CollectedProxy
	cachedValid := 0
	alreadyProcessed := 0

	if unifiedCache != nil {
		// 获取缓存中的有效代理
		validProxies = unifiedCache.GetValidProxies()
		cachedValid = len(validProxies)

		// 分离需要验证的代理
		var cachedValidProxies []*CollectedProxy
		for _, proxy := range proxies {
			status, entry := unifiedCache.GetProxyStatus(proxy.Host, proxy.Port)

			switch status {
			case ProxyStatusValid:
				// 代理仍然有效，添加到有效列表，跳过验证
				cachedValidProxies = append(cachedValidProxies, proxy)
				alreadyProcessed++
				continue
			case ProxyStatusInvalid:
				// 代理已知无效但在进度缓存期内，跳过验证
				if unifiedCache.IsProcessed(proxy.Host, proxy.Port) {
					alreadyProcessed++
					continue
				}
				// 否则重新验证（可能状态已改变）
				toVerify = append(toVerify, proxy)
			default:
				// 检查是否已处理过（即使状态为 Unknown）
				if unifiedCache.IsProcessed(proxy.Host, proxy.Port) {
					// 已处理过但有效性过期，根据历史记录决定是否重新验证
					if entry != nil && entry.VerifyCount > 0 {
						// 如果历史上验证过且失败次数较多，跳过重新验证
						if entry.SuccessCount == 0 && entry.VerifyCount >= 3 {
							alreadyProcessed++
							continue
						}
					}
				}
				// 未知状态或需要重新验证
				toVerify = append(toVerify, proxy)
			}
		}

		// 合并缓存中的有效代理
		validProxies = append(validProxies, cachedValidProxies...)
		cachedValid = len(cachedValidProxies)

		// 更新会话进度
		unifiedCache.UpdateSessionProgress(
			len(proxies),
			len(proxies)-len(toVerify),
			len(validProxies),
			alreadyProcessed,
		)

		// 获取会话进度信息用于断点续验日志
		sessionInfo := unifiedCache.GetSessionProgress()
		c.logger.WithFields(logrus.Fields{
			"session_id":         sessionInfo.SessionID,
			"previous_processed": sessionInfo.ProcessedProxies,
			"previous_valid":     sessionInfo.ValidProxies,
			"previous_invalid":   sessionInfo.InvalidProxies,
			"resume_efficiency":  fmt.Sprintf("%.1f%%", float64(alreadyProcessed)/float64(len(proxies))*100),
		}).Info("断点续验: 从缓存中恢复验证进度")
	} else {
		// 没有缓存，验证所有代理
		toVerify = proxies
	}

	c.logger.WithFields(logrus.Fields{
		"total_proxies":      len(proxies),
		"cached_valid":       cachedValid,
		"already_processed":  alreadyProcessed,
		"to_verify":          len(toVerify),
		"valid_so_far":       len(validProxies),
		"concurrent_workers": c.config.ConcurrentWorkers,
		"cache_enabled":      c.config.CacheEnabled,
	}).Info("Cache analysis completed, starting verification")

	// 创建进度显示器
	initialProcessed := int64(len(proxies) - len(toVerify))
	progressDisplay := NewVerificationProgressDisplay(c.config, c.logger, len(proxies), initialProcessed)
	if progressDisplay != nil {
		defer progressDisplay.Stop()

		// 更新初始进度显示
		if initialProcessed > 0 {
			progressDisplay.UpdateMeta(ProgressUpdateMeta{
				processed:  initialProcessed,
				successful: int64(len(validProxies)),
				failed:     initialProcessed - int64(len(validProxies)),
			})
		}
	}

	if len(toVerify) == 0 {
		c.logger.WithFields(logrus.Fields{
			"total_proxies": len(proxies),
			"valid_proxies": len(validProxies),
		}).Info("All proxies already processed, verification complete")

		return validProxies, nil
	}

	// 验证剩余的代理
	newValidProxies, err := c.verifyProxiesWithUnifiedCache(ctx, toVerify, unifiedCache, progressDisplay, initialProcessed)
	if err != nil {
		c.logger.WithError(err).Error("Verification failed")
		return validProxies, err
	}

	// 合并结果
	validProxies = append(validProxies, newValidProxies...)

	// 最终保存缓存状态和会话进度
	if unifiedCache != nil {
		if progressDisplay != nil {
			processed, successful, failed, _ := progressDisplay.GetStats()
			unifiedCache.UpdateSessionProgress(
				len(proxies),
				int(processed),
				int(successful),
				int(failed),
			)
		}

		if err := unifiedCache.Save(); err != nil {
			c.logger.WithError(err).Error("Failed to save final unified cache state")
		}
	}

	// 最终统计
	if progressDisplay != nil {
		processed, successful, failed, elapsed := progressDisplay.GetStats()
		c.logger.WithFields(logrus.Fields{
			"total_proxies":    len(proxies),
			"processed":        processed,
			"successful":       successful,
			"failed":           failed,
			"success_rate":     fmt.Sprintf("%.2f%%", float64(successful)/float64(processed)*100),
			"total_time":       elapsed.String(),
			"average_speed":    fmt.Sprintf("%.2f proxies/sec", float64(processed)/elapsed.Seconds()),
			"cache_hits":       unifiedCache.GetStats().CacheHits,
			"cache_efficiency": fmt.Sprintf("%.1f%%", float64(len(proxies)-len(toVerify))/float64(len(proxies))*100),
		}).Info("Unified cache verification completed")
	}

	return validProxies, nil
}

// verifyProxiesWithUnifiedCache 使用统一缓存系统验证代理列表
func (c *ProxyCollector) verifyProxiesWithUnifiedCache(ctx context.Context, proxies []*CollectedProxy, unifiedCache *Cache, progressDisplay *VerificationProgressDisplay, initialProcessed int64) ([]*CollectedProxy, error) {
	if len(proxies) == 0 {
		return nil, nil
	}

	c.logger.WithFields(logrus.Fields{
		"proxy_count":       len(proxies),
		"verification_mode": "unified_cache_engine",
	}).Info("Starting verification with unified cache system")

	// 创建极致高并发验证器配置
	verifierConfig := concurrent.DefaultProxyVerifierConfig()

	// 根据代理数量动态调整配置
	proxyCount := len(proxies)
	if proxyCount > 100000 {
		verifierConfig.MaxWorkers = verifierConfig.MaxWorkers * 2 // 大量代理时翻倍
		verifierConfig.MaxScaleWorkers = verifierConfig.MaxScaleWorkers * 2
	} else if proxyCount > 50000 {
		verifierConfig.MaxWorkers = int(float64(verifierConfig.MaxWorkers) * 1.5) // 中等数量时1.5倍
	}

	// 使用配置中的设置
	verifierConfig.TestURL = c.config.TestURL
	verifierConfig.VerificationTimeout = c.config.VerificationTimeout
	verifierConfig.ConnectTimeout = c.config.RequestTimeout
	verifierConfig.RetryAttempts = c.config.MaxRetries

	c.logger.WithFields(logrus.Fields{
		"max_workers":       verifierConfig.MaxWorkers,
		"max_scale_workers": verifierConfig.MaxScaleWorkers,
		"test_url":          verifierConfig.TestURL,
		"timeout":           verifierConfig.VerificationTimeout,
		"expected_speed":    fmt.Sprintf("%.0f proxies/sec", float64(verifierConfig.MaxWorkers)*5),
	}).Info("Unified cache engine configuration")

	// 创建极致高并发验证器
	concurrentVerifier := concurrent.NewProxyVerifier(verifierConfig, c.logger)
	defer concurrentVerifier.Stop()

	// 转换代理数据
	proxyDataList := make([]concurrent.ProxyData, len(proxies))
	for i, proxy := range proxies {
		proxyDataList[i] = concurrent.ProxyData{
			Host:           proxy.Host,
			Port:           proxy.Port,
			Type:           string(proxy.Type),
			TestURL:        c.config.TestURL,
			Timeout:        c.config.VerificationTimeout,
			ConnectTimeout: c.config.RequestTimeout,
		}
	}

	// 创建组合回调函数
	var combinedCallback = func(completed, total, successful, failed int64) {
		// 计算累积进度（基准值 + 当前验证进度）
		totalProcessed := initialProcessed + completed
		totalSuccessful := successful // 仅当前验证成功数，基准值已在外部处理
		totalFailed := failed

		// 1. ProgressBar 实时显示更新
		if progressDisplay != nil {
			progressDisplay.UpdateMeta(ProgressUpdateMeta{
				processed:  totalProcessed,
				successful: totalSuccessful,
				failed:     totalFailed,
			})
		}

		// 2. 统一缓存系统自动更新会话进度
		if unifiedCache != nil {
			sessionInfo := unifiedCache.GetSessionProgress()
			unifiedCache.UpdateSessionProgress(
				sessionInfo.TotalProxies,
				int(totalProcessed),
				int(totalSuccessful),
				int(totalFailed),
			)
		}

		// 3. 定期保存统一缓存到文件（更频繁的保存以支持断点续验）
		if unifiedCache != nil && (completed%50 == 0 || completed%100 > 10) && completed > 0 {
			go func() {
				// 更新会话进度信息
				unifiedCache.UpdateSessionProgress(
					len(proxies),
					int(totalProcessed),
					int(successful),
					int(failed),
				)

				if err := unifiedCache.Save(); err != nil {
					c.logger.WithError(err).Debug("Failed to save unified cache during verification")
				} else {
					c.logger.WithFields(logrus.Fields{
						"completed":  totalProcessed,
						"successful": successful,
						"failed":     failed,
					}).Debug("Unified cache saved during verification progress")
				}
			}()
		}
	}

	// 执行极致高并发验证
	results, err := concurrentVerifier.VerifyProxies(ctx, proxyDataList, combinedCallback)
	if err != nil {
		return nil, fmt.Errorf("unified cache verification failed: %w", err)
	}

	// 处理结果并更新原始代理对象和统一缓存
	var validProxies []*CollectedProxy
	for i, result := range results {
		if i < len(proxies) && result != nil {
			proxy := proxies[i]

			// 更新代理状态
			proxy.Verified = result.Success
			if result.Success {
				proxy.Status = models.ProxyStatusActive
				if result.ResponseTime > 0 {
					responseTime := result.ResponseTime.Milliseconds()
					proxy.ResponseTime = &responseTime
				}
				validProxies = append(validProxies, proxy)

				// 更新统一缓存 - 有效代理
				if unifiedCache != nil {
					unifiedCache.UpdateProxyResult(proxy, true, proxy.ResponseTime, "")
				}
			} else {
				proxy.Status = models.ProxyStatusFailed
				proxy.Error = result.Error

				// 更新统一缓存 - 无效代理
				if unifiedCache != nil {
					unifiedCache.UpdateProxyResult(proxy, false, nil, result.Error)
				}
			}
		}
	}

	// 获取最终统计
	completed, total, successful, failed, elapsed := concurrentVerifier.GetStats()

	// 最终保存统一缓存
	if unifiedCache != nil {
		if err := unifiedCache.Save(); err != nil {
			c.logger.WithError(err).Error("Failed to save final unified cache")
		}
	}

	c.logger.WithFields(logrus.Fields{
		"total_processed": completed,
		"total_proxies":   total,
		"successful":      successful,
		"failed":          failed,
		"valid_proxies":   len(validProxies),
		"success_rate":    fmt.Sprintf("%.2f%%", float64(successful)/float64(completed)*100),
		"elapsed_time":    elapsed,
		"proxies_per_sec": fmt.Sprintf("%.2f", float64(completed)/elapsed.Seconds()),
	}).Info("Unified cache verification completed")

	return validProxies, nil
}

// verifyProxy 验证单个代理（保留用于小批量验证或调试目的）
// 注意：大批量验证请使用 verifyProxiesHighConcurrency 方法
func (c *ProxyCollector) verifyProxy(ctx context.Context, proxy *CollectedProxy) *VerificationResult {
	logger := c.logger.WithFields(logrus.Fields{
		"host": proxy.Host,
		"port": proxy.Port,
		"type": proxy.Type,
	})

	startTime := time.Now()

	// 创建代理URL
	proxyURL, err := c.buildProxyURL(proxy)
	if err != nil {
		logger.WithError(err).Debug("Failed to build proxy URL")
		return &VerificationResult{
			Proxy:   proxy,
			Success: false,
			Error:   fmt.Sprintf("invalid proxy URL: %v", err),
		}
	}

	// 创建HTTP客户端
	client, cleanup, err := c.createProxyClient(proxyURL)
	if err != nil {
		logger.WithError(err).Debug("Failed to create proxy client")
		return &VerificationResult{
			Proxy:   proxy,
			Success: false,
			Error:   fmt.Sprintf("failed to create client: %v", err),
		}
	}
	defer cleanup() // 确保连接被清理

	// 创建请求上下文
	verifyCtx, cancel := context.WithTimeout(ctx, c.config.VerificationTimeout)
	defer cancel()

	// 发送测试请求
	req, err := http.NewRequestWithContext(verifyCtx, "GET", c.config.TestURL, nil)
	if err != nil {
		logger.WithError(err).Debug("Failed to create test request")
		return &VerificationResult{
			Proxy:   proxy,
			Success: false,
			Error:   fmt.Sprintf("failed to create request: %v", err),
		}
	}

	// 设置请求头
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Accept", "application/json")

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		logger.WithError(err).Debug("Proxy verification failed")
		return &VerificationResult{
			Proxy:   proxy,
			Success: false,
			Error:   fmt.Sprintf("request failed: %v", err),
		}
	}
	defer resp.Body.Close()

	responseTime := time.Since(startTime).Milliseconds()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		logger.WithField("status_code", resp.StatusCode).Debug("Non-200 response")
		return &VerificationResult{
			Proxy:        proxy,
			Success:      false,
			ResponseTime: responseTime,
			Error:        fmt.Sprintf("HTTP %d", resp.StatusCode),
		}
	}

	// 解析响应获取真实IP
	var ipResp IPResponse
	if err := json.NewDecoder(resp.Body).Decode(&ipResp); err != nil {
		logger.WithError(err).Debug("Failed to decode response")
		return &VerificationResult{
			Proxy:        proxy,
			Success:      false,
			ResponseTime: responseTime,
			Error:        fmt.Sprintf("failed to decode response: %v", err),
		}
	}

	// 检测匿名级别
	anonymityLevel := c.detectAnonymityLevel(proxy.Host, ipResp.Origin)

	logger.WithFields(logrus.Fields{
		"response_time":   responseTime,
		"real_ip":         ipResp.Origin,
		"anonymity_level": anonymityLevel,
	}).Debug("Proxy verification successful")

	return &VerificationResult{
		Proxy:          proxy,
		Success:        true,
		ResponseTime:   responseTime,
		RealIP:         ipResp.Origin,
		AnonymityLevel: anonymityLevel,
	}
}

// buildProxyURL 构建代理URL
func (c *ProxyCollector) buildProxyURL(proxy *CollectedProxy) (*url.URL, error) {
	var scheme string
	switch proxy.Type {
	case models.ProxyTypeHTTP:
		scheme = "http"
	case models.ProxyTypeHTTPS:
		scheme = "https"
	case models.ProxyTypeSOCKS5:
		scheme = "socks5"
	default:
		return nil, fmt.Errorf("unsupported proxy type: %s", proxy.Type)
	}

	proxyURL := &url.URL{
		Scheme: scheme,
		Host:   fmt.Sprintf("%s:%d", proxy.Host, proxy.Port),
	}

	// 添加认证信息
	if proxy.Username != "" && proxy.Password != "" {
		proxyURL.User = url.UserPassword(proxy.Username, proxy.Password)
	}

	return proxyURL, nil
}

// createProxyClient 创建代理HTTP客户端（优化版）
func (c *ProxyCollector) createProxyClient(proxyURL *url.URL) (*http.Client, func(), error) {
	transport := &http.Transport{
		Proxy: http.ProxyURL(proxyURL),
		DialContext: (&net.Dialer{
			Timeout:   c.config.VerificationTimeout,
			KeepAlive: 30 * time.Second,
		}).DialContext,
		TLSHandshakeTimeout:   10 * time.Second,
		ResponseHeaderTimeout: c.config.VerificationTimeout,
		ExpectContinueTimeout: 1 * time.Second,
		MaxIdleConns:          10,
		IdleConnTimeout:       30 * time.Second,
		DisableCompression:    true,
	}

	client := &http.Client{
		Transport: transport,
		Timeout:   c.config.VerificationTimeout,
	}

	// 返回清理函数
	cleanup := func() {
		transport.CloseIdleConnections()
	}

	return client, cleanup, nil
}

// detectAnonymityLevel 检测匿名级别
func (c *ProxyCollector) detectAnonymityLevel(proxyIP, realIP string) models.AnonymityLevel {
	// 如果真实IP包含代理IP，说明是透明代理
	if strings.Contains(realIP, proxyIP) {
		return models.AnonymityLevelTransparent
	}

	// 如果真实IP是单个IP且不是代理IP，说明是匿名代理
	ips := strings.Split(realIP, ",")
	if len(ips) == 1 {
		cleanIP := strings.TrimSpace(ips[0])
		if cleanIP != proxyIP && c.isValidIP(cleanIP) {
			return models.AnonymityLevelAnonymous
		}
	}

	// 如果有多个IP或格式复杂，可能是高匿代理
	if len(ips) > 1 {
		return models.AnonymityLevelElite
	}

	return models.AnonymityLevelUnknown
}

// calculateQualityScore 计算质量分数
func (c *ProxyCollector) calculateQualityScore(responseTime int64, anonymityLevel models.AnonymityLevel, proxyType models.ProxyType) float64 {
	var score float64 = 0.0

	// 响应时间评分 (40%)
	if responseTime <= 1000 { // 1秒以内
		score += 0.4
	} else if responseTime <= 3000 { // 3秒以内
		score += 0.3
	} else if responseTime <= 5000 { // 5秒以内
		score += 0.2
	} else {
		score += 0.1
	}

	// 匿名级别评分 (30%)
	switch anonymityLevel {
	case models.AnonymityLevelElite:
		score += 0.3
	case models.AnonymityLevelAnonymous:
		score += 0.25
	case models.AnonymityLevelTransparent:
		score += 0.15
	default:
		score += 0.1
	}

	// 代理类型评分 (20%)
	switch proxyType {
	case models.ProxyTypeHTTPS:
		score += 0.2
	case models.ProxyTypeHTTP:
		score += 0.15
	case models.ProxyTypeSOCKS5:
		score += 0.18
	}

	// 基础可用性评分 (10%) - 如果调用此方法说明代理可用
	score += 0.1

	// 确保分数在0-1之间
	if score > 1.0 {
		score = 1.0
	}
	if score < 0.0 {
		score = 0.0
	}

	return score
}
