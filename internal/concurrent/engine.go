// Package concurrent 提供极致高并发验证引擎
package concurrent

import (
	"context"
	"runtime"
	"sync"
	"sync/atomic"
	"time"

	"github.com/sirupsen/logrus"
)

// Task 表示一个验证任务
type Task struct {
	ID   int         // 任务ID
	Data interface{} // 任务数据
}

// Result 表示验证结果
type Result struct {
	TaskID  int           // 任务ID
	Data    interface{}   // 原始数据
	Success bool          // 是否成功
	Error   string        // 错误信息
	Elapsed time.Duration // 执行时间
}

// WorkerFunc 工作函数类型
type WorkerFunc func(ctx context.Context, data interface{}) (success bool, err error)

// ProgressCallback 进度回调函数类型
type ProgressCallback func(completed, total, successful, failed int64)

// Config 极致高并发引擎配置
type Config struct {
	// 核心配置
	MaxWorkers       int // 最大工作协程数
	QueueSize        int // 任务队列大小
	ResultBufferSize int // 结果缓冲区大小

	// 性能优化
	WorkerPoolSize int           // 工作池大小
	BatchSize      int           // 批处理大小
	FlushInterval  time.Duration // 刷新间隔

	// 超时和重试
	TaskTimeout   time.Duration // 单任务超时
	RetryAttempts int           // 重试次数
	RetryDelay    time.Duration // 重试延迟

	// 监控和报告
	ProgressInterval time.Duration // 进度报告间隔
	EnableMetrics    bool          // 启用指标收集

	// 动态调整
	AutoScale       bool    // 自动扩缩容
	ScaleThreshold  float64 // 扩缩容阈值
	MaxScaleWorkers int     // 最大扩容工作数
}

// DefaultConfig 返回极致性能配置（针对500-1000+/s优化）
func DefaultConfig() *Config {
	cpuCount := runtime.NumCPU()
	return &Config{
		MaxWorkers:       cpuCount * 100,         // 激进增加到 CPU * 100
		QueueSize:        100000,                 // 大幅增加队列大小
		ResultBufferSize: 50000,                  // 大幅增加结果缓冲
		WorkerPoolSize:   cpuCount * 50,          // 增加工作池大小
		BatchSize:        5000,                   // 增加批处理大小
		FlushInterval:    50 * time.Millisecond,  // 减少刷新间隔提高响应速度
		TaskTimeout:      3 * time.Second,        // 减少任务超时提高速度
		RetryAttempts:    0,                      // 完全禁用重试
		RetryDelay:       0,                      // 无重试延迟
		ProgressInterval: 500 * time.Millisecond, // 更频繁的进度报告
		EnableMetrics:    false,                  // 禁用指标减少开销
		AutoScale:        true,                   // 启用自动扩缩容
		ScaleThreshold:   0.7,                    // 70%负载时扩容
		MaxScaleWorkers:  cpuCount * 200,         // 允许扩容到CPU*200
	}
}

// Engine 极致高并发验证引擎
type Engine struct {
	config *Config
	logger *logrus.Logger

	// 核心组件
	taskQueue   chan *Task
	resultQueue chan *Result
	workerFunc  WorkerFunc

	// 工作协程管理
	workers       []*Worker
	workerPool    sync.Pool
	activeWorkers int64
	workersMu     sync.RWMutex

	// 统计信息
	totalTasks      int64
	completedTasks  int64
	successfulTasks int64
	failedTasks     int64
	startTime       time.Time

	// 控制信号
	ctx    context.Context
	cancel context.CancelFunc
	stopCh chan struct{}
	doneCh chan struct{}

	// 同步原语
	wg       sync.WaitGroup
	resultWg sync.WaitGroup

	// 动态扩缩容
	scaleTimer *time.Timer
	scaleMu    sync.RWMutex

	// 结果收集
	results     []*Result
	resultsMu   sync.RWMutex
	resultBatch []*Result
	batchMu     sync.Mutex
	batchSize   int
}

// Worker 工作协程
type Worker struct {
	id         int
	engine     *Engine
	taskCh     chan *Task
	resultCh   chan *Result
	stopCh     chan struct{}
	shouldStop int32 // 原子操作标志
}

// NewEngine 创建新的极致高并发引擎
func NewEngine(config *Config, logger *logrus.Logger, workerFunc WorkerFunc) *Engine {
	if config == nil {
		config = DefaultConfig()
	}

	if logger == nil {
		logger = logrus.New()
	}

	ctx, cancel := context.WithCancel(context.Background())

	engine := &Engine{
		config:      config,
		logger:      logger,
		workerFunc:  workerFunc,
		taskQueue:   make(chan *Task, config.QueueSize),
		resultQueue: make(chan *Result, config.ResultBufferSize),
		ctx:         ctx,
		cancel:      cancel,
		stopCh:      make(chan struct{}),
		doneCh:      make(chan struct{}),
		results:     make([]*Result, 0),
		resultBatch: make([]*Result, 0, config.BatchSize),
		batchSize:   config.BatchSize,
	}

	// 初始化工作池
	engine.workerPool.New = func() interface{} {
		return &Worker{
			engine:     engine,
			taskCh:     make(chan *Task, 1),
			resultCh:   make(chan *Result, 1),
			stopCh:     make(chan struct{}),
			shouldStop: 0,
		}
	}

	return engine
}

// Start 启动引擎
func (e *Engine) Start() error {
	e.startTime = time.Now()

	e.logger.WithFields(logrus.Fields{
		"max_workers":        e.config.MaxWorkers,
		"queue_size":         e.config.QueueSize,
		"result_buffer_size": e.config.ResultBufferSize,
		"auto_scale":         e.config.AutoScale,
	}).Info("Starting ultra-concurrent verification engine")

	// 启动初始工作协程
	e.startWorkers(e.config.MaxWorkers)

	// 启动结果收集器
	go e.resultCollector()

	// 启动动态扩缩容
	if e.config.AutoScale {
		go e.autoScaler()
	}

	return nil
}

// Stop 停止引擎
func (e *Engine) Stop() error {
	e.logger.Info("Stopping ultra-concurrent verification engine")

	// 使用 sync.Once 确保只关闭一次
	var once sync.Once

	once.Do(func() {
		// 发送停止信号
		select {
		case <-e.stopCh:
			// 已经关闭
		default:
			close(e.stopCh)
		}

		e.cancel()

		// 等待所有工作协程完成
		e.wg.Wait()

		// 任务队列已在 distributeTask 中关闭

		// 关闭结果队列
		defer func() {
			if r := recover(); r != nil {
				// 忽略关闭已关闭通道的panic
			}
		}()
		close(e.resultQueue)

		// 等待结果收集完成
		e.resultWg.Wait()

		// 停止扩缩容定时器
		if e.scaleTimer != nil {
			e.scaleTimer.Stop()
		}

		// 关闭完成通道
		select {
		case <-e.doneCh:
			// 已经关闭
		default:
			close(e.doneCh)
		}
	})

	elapsed := time.Since(e.startTime)
	e.logger.WithFields(logrus.Fields{
		"total_tasks":      e.totalTasks,
		"completed_tasks":  e.completedTasks,
		"successful_tasks": e.successfulTasks,
		"failed_tasks":     e.failedTasks,
		"elapsed_time":     elapsed,
		"tasks_per_sec":    float64(e.completedTasks) / elapsed.Seconds(),
	}).Info("Ultra-concurrent verification engine stopped")

	return nil
}

// Execute 执行批量任务
func (e *Engine) Execute(ctx context.Context, data []interface{}, progressCallback ProgressCallback) ([]*Result, error) {
	if len(data) == 0 {
		return nil, nil
	}

	e.totalTasks = int64(len(data))
	e.completedTasks = 0
	e.successfulTasks = 0
	e.failedTasks = 0

	e.logger.WithFields(logrus.Fields{
		"total_tasks":    len(data),
		"max_workers":    e.config.MaxWorkers,
		"expected_speed": float64(e.config.MaxWorkers) * 10,
	}).Info("Starting ultra-concurrent task execution")

	// 启动引擎
	if err := e.Start(); err != nil {
		return nil, err
	}
	defer e.Stop()

	// 启动进度回调
	if progressCallback != nil {
		go e.progressReporter(ctx, progressCallback)
	}

	// 分发任务
	go e.distributeTask(data)

	// 等待完成
	e.waitForCompletion(ctx)

	// 返回结果
	e.resultsMu.RLock()
	results := make([]*Result, len(e.results))
	copy(results, e.results)
	e.resultsMu.RUnlock()

	return results, nil
}

// GetStats 获取统计信息
func (e *Engine) GetStats() (completed, total, successful, failed int64, elapsed time.Duration) {
	return atomic.LoadInt64(&e.completedTasks),
		e.totalTasks,
		atomic.LoadInt64(&e.successfulTasks),
		atomic.LoadInt64(&e.failedTasks),
		time.Since(e.startTime)
}

// startWorkers 启动工作协程
func (e *Engine) startWorkers(count int) {
	e.workers = make([]*Worker, count)
	for i := range count {
		worker := e.workerPool.Get().(*Worker)
		worker.id = i
		e.workers[i] = worker

		e.wg.Add(1)
		go e.runWorker(worker)
	}

	atomic.StoreInt64(&e.activeWorkers, int64(count))

	e.logger.WithField("worker_count", count).Info("Started workers")
}

// runWorker 运行单个工作协程
func (e *Engine) runWorker(worker *Worker) {
	defer e.wg.Done()
	defer e.workerPool.Put(worker)

	for {
		// 检查是否需要停止
		if atomic.LoadInt32(&worker.shouldStop) == 1 {
			return
		}

		select {
		case task := <-e.taskQueue:
			if task == nil {
				return // 队列关闭
			}
			// 再次检查停止标志
			if atomic.LoadInt32(&worker.shouldStop) == 1 {
				return
			}
			e.processTask(worker, task)

		case <-e.stopCh:
			return

		case <-e.ctx.Done():
			return
		}
	}
}

// processTask 处理单个任务
func (e *Engine) processTask(_ *Worker, task *Task) {
	startTime := time.Now()

	// 创建任务上下文
	taskCtx, cancel := context.WithTimeout(e.ctx, e.config.TaskTimeout)
	defer cancel()

	var success bool
	var err error

	// 重试机制
	for attempt := 0; attempt <= e.config.RetryAttempts; attempt++ {
		if attempt > 0 {
			// 重试延迟
			select {
			case <-time.After(e.config.RetryDelay):
			case <-taskCtx.Done():
				return // 上下文取消，直接返回
			}
		}

		success, err = e.workerFunc(taskCtx, task.Data)
		if success || err == nil {
			break // 成功或无错误，退出重试
		}
	}

	elapsed := time.Since(startTime)

	// 创建结果
	result := &Result{
		TaskID:  task.ID,
		Data:    task.Data,
		Success: success,
		Error: func() string {
			if err != nil {
				return err.Error()
			}
			return ""
		}(),
		Elapsed: elapsed,
	}

	// 发送结果
	select {
	case e.resultQueue <- result:
	case <-e.ctx.Done():
		return

	}

	// 更新统计
	atomic.AddInt64(&e.completedTasks, 1)
	if success {
		atomic.AddInt64(&e.successfulTasks, 1)
	} else {
		atomic.AddInt64(&e.failedTasks, 1)
	}
}

// resultCollector 结果收集器
func (e *Engine) resultCollector() {
	e.resultWg.Add(1)
	defer e.resultWg.Done()

	// 批量刷新定时器
	flushTicker := time.NewTicker(e.config.FlushInterval)
	defer flushTicker.Stop()

	for {
		select {
		case result, ok := <-e.resultQueue:
			if !ok {
				// 通道关闭，刷新剩余批次
				e.flushBatch()
				return
			}
			e.addToBatch(result)

		case <-flushTicker.C:
			// 定期刷新批次
			e.flushBatch()
		}
	}
}

// addToBatch 添加结果到批次
func (e *Engine) addToBatch(result *Result) {
	e.batchMu.Lock()
	defer e.batchMu.Unlock()

	e.resultBatch = append(e.resultBatch, result)
	if len(e.resultBatch) >= e.batchSize {
		e.flushBatchUnsafe()
	}
}

// flushBatch 刷新批次（安全版本）
func (e *Engine) flushBatch() {
	e.batchMu.Lock()
	defer e.batchMu.Unlock()
	e.flushBatchUnsafe()
}

// flushBatchUnsafe 刷新批次（非安全版本，需要持有锁）
func (e *Engine) flushBatchUnsafe() {
	if len(e.resultBatch) == 0 {
		return
	}

	// 复制当前批次
	batch := make([]*Result, len(e.resultBatch))
	copy(batch, e.resultBatch)

	// 清空批次
	e.resultBatch = e.resultBatch[:0]

	// 添加到结果集
	e.resultsMu.Lock()
	e.results = append(e.results, batch...)
	e.resultsMu.Unlock()
}

// distributeTask 分发任务（最大化并发版本）
func (e *Engine) distributeTask(data []interface{}) {
	defer func() {
		// 安全关闭任务队列
		defer func() {
			if r := recover(); r != nil {
				// 忽略关闭已关闭通道的panic
			}
		}()
		close(e.taskQueue)
	}()

	// 移除背压控制，直接快速分发任务
	for i, item := range data {
		task := &Task{
			ID:   i,
			Data: item,
		}

		select {
		case e.taskQueue <- task:
			// 任务成功分发
		case <-e.ctx.Done():
			return
		default:
			// 如果队列满了，尝试等待一个极短时间后再试
			select {
			case e.taskQueue <- task:
			case <-time.After(time.Microsecond): // 极短等待
			case <-e.ctx.Done():
				return
			}
		}
	}
}

// waitForCompletion 等待完成
func (e *Engine) waitForCompletion(ctx context.Context) {
	ticker := time.NewTicker(100 * time.Millisecond)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			completed := atomic.LoadInt64(&e.completedTasks)
			if completed >= e.totalTasks {
				return
			}
		case <-ctx.Done():
			return
		case <-e.doneCh:
			return
		}
	}
}

// progressReporter 进度报告器
func (e *Engine) progressReporter(ctx context.Context, callback ProgressCallback) {
	ticker := time.NewTicker(e.config.ProgressInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			completed := atomic.LoadInt64(&e.completedTasks)
			successful := atomic.LoadInt64(&e.successfulTasks)
			failed := atomic.LoadInt64(&e.failedTasks)
			callback(completed, e.totalTasks, successful, failed)

		case <-ctx.Done():
			return
		case <-e.doneCh:
			return
		}
	}
}

// autoScaler 自动扩缩容
func (e *Engine) autoScaler() {
	ticker := time.NewTicker(1 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			e.checkAndScale()

		case <-e.stopCh:
			return
		case <-e.ctx.Done():
			return
		}
	}
}

// checkAndScale 检查并扩缩容
func (e *Engine) checkAndScale() {
	e.scaleMu.Lock()
	defer e.scaleMu.Unlock()

	queueLen := len(e.taskQueue)
	activeWorkers := int(atomic.LoadInt64(&e.activeWorkers))
	completed := atomic.LoadInt64(&e.completedTasks)
	elapsed := time.Since(e.startTime).Seconds()

	// 如果任务队列有积压且处理速度慢，立即扩容
	if queueLen > 1000 && activeWorkers < e.config.MaxScaleWorkers {
		// 根据队列积压量决定扩容数量
		scaleAmount := min(queueLen/100, e.config.MaxScaleWorkers-activeWorkers)
		if scaleAmount > 0 {
			e.scaleUp(scaleAmount)
			return
		}
	}

	// 基于处理速度的扩容策略
	if elapsed > 5 && completed > 0 { // 运行超过5秒且有进度
		currentSpeed := float64(completed) / elapsed
		expectedSpeed := float64(activeWorkers) * 10 // 期望每个worker每秒处理10个任务

		// 如果实际速度远低于期望，且还有任务积压，扩容
		if currentSpeed < expectedSpeed*0.5 && queueLen > 0 && activeWorkers < e.config.MaxScaleWorkers {
			scaleAmount := min(activeWorkers/2, e.config.MaxScaleWorkers-activeWorkers)
			if scaleAmount > 0 {
				e.scaleUp(scaleAmount)
			}
		}
	}

	// 缩容条件：队列空且worker过多
	if queueLen == 0 && activeWorkers > e.config.MaxWorkers {
		reduceWorkers := min(activeWorkers/4, activeWorkers-e.config.MaxWorkers)
		if reduceWorkers > 0 {
			e.scaleDown(reduceWorkers)
		}
	}
}

// scaleUp 扩容
func (e *Engine) scaleUp(count int) {
	e.logger.WithFields(logrus.Fields{
		"current_workers": atomic.LoadInt64(&e.activeWorkers),
		"scale_up_count":  count,
	}).Debug("Scaling up workers")

	for i := 0; i < count; i++ {
		worker := e.workerPool.Get().(*Worker)
		worker.id = int(atomic.LoadInt64(&e.activeWorkers)) + i

		e.wg.Add(1)
		go e.runWorker(worker)
	}

	atomic.AddInt64(&e.activeWorkers, int64(count))
}

// scaleDown 缩容
func (e *Engine) scaleDown(count int) {
	e.logger.WithFields(logrus.Fields{
		"current_workers":  atomic.LoadInt64(&e.activeWorkers),
		"scale_down_count": count,
	}).Info("Scaling down workers")

	e.workersMu.Lock()
	defer e.workersMu.Unlock()

	// 标记需要停止的worker
	stopped := 0
	for i := len(e.workers) - 1; i >= 0 && stopped < count; i-- {
		if e.workers[i] != nil {
			// 使用原子操作标记worker停止
			if atomic.CompareAndSwapInt32(&e.workers[i].shouldStop, 0, 1) {
				e.workers[i] = nil
				stopped++
			}
		}
	}

	atomic.AddInt64(&e.activeWorkers, -int64(stopped))
}
