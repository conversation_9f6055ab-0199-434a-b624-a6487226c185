package concurrent

import (
	"context"
	"fmt"
	"net"
	"net/http"
	"net/url"
	"proxyFlow/internal/utils"
	"runtime"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// ProxyCache 代理缓存接口
type ProxyCache interface {
	Set(host string, port int, isValid bool)
	IsValid(host string, port int) bool
	Save() error
}

// ProxyData 代理数据结构
type ProxyData struct {
	Host           string
	Port           int
	Type           string
	Username       string
	Password       string
	TestURL        string
	Timeout        time.Duration
	ConnectTimeout time.Duration
}

// ProxyResult 代理验证结果
type ProxyResult struct {
	Host         string
	Port         int
	Success      bool
	Error        string
	ResponseTime time.Duration
	StatusCode   int
	RemoteIP     string
}

// ProxyVerifierConfig 代理验证器配置
type ProxyVerifierConfig struct {
	// 基础配置
	MaxWorkers          int           // 最大工作协程数
	TestURL             string        // 测试URL
	VerificationTimeout time.Duration // 验证超时
	ConnectTimeout      time.Duration // 连接超时

	// 性能优化
	QueueSize        int // 队列大小
	ResultBufferSize int // 结果缓冲大小
	BatchSize        int // 批处理大小

	// 重试配置
	RetryAttempts int           // 重试次数
	RetryDelay    time.Duration // 重试延迟

	// 高级特性
	EnableAutoScale  bool          // 启用自动扩缩容
	MaxScaleWorkers  int           // 最大扩容工作数
	EnableMetrics    bool          // 启用指标收集
	ProgressInterval time.Duration // 进度报告间隔

	// HTTP客户端配置
	MaxIdleConns        int           // 最大空闲连接
	MaxIdleConnsPerHost int           // 每个主机最大空闲连接
	IdleConnTimeout     time.Duration // 空闲连接超时
	DisableKeepAlives   bool          // 禁用连接复用
}

// 返回极致性能配置（针对500-1000+/s优化）
func DefaultProxyVerifierConfig() *ProxyVerifierConfig {
	cpuCount := runtime.NumCPU()
	// 激进的工作者数量配置以达到500-1000+/s
	maxWorkers := cpuCount * 100 // 大幅增加到 CPU * 100
	if maxWorkers < 1000 {       // 最低保证1000个工作者
		maxWorkers = 1000
	}
	if maxWorkers > 3000 { // 硬限制最大3000个工作者
		maxWorkers = 3000
	}
	maxWorkers = 2 // TODO:开发调试需要，maxWorkers 暂时设置为 2，开发完毕后删除此行
	return &ProxyVerifierConfig{
		MaxWorkers:          maxWorkers,              // 激进的工作者数量
		TestURL:             "http://httpbin.org/ip", // 单一测试URL提高效率
		VerificationTimeout: 5 * time.Second,         // 降低超时时间提高速度
		ConnectTimeout:      3 * time.Second,         // 极短连接超时
		QueueSize:           100000,                  // 大幅增加队列大小
		ResultBufferSize:    50000,                   // 大幅增加结果缓冲
		BatchSize:           5000,                    // 增加批处理大小
		RetryAttempts:       1,                       // 完全禁用重试
		RetryDelay:          0,                       // 无重试延迟
		EnableAutoScale:     true,                    // 启用自动扩缩容
		MaxScaleWorkers:     maxWorkers * 2,          // 允许扩容到2倍
		EnableMetrics:       false,                   // 禁用指标收集减少开销
		ProgressInterval:    500 * time.Millisecond,  // 更频繁的进度更新
		MaxIdleConns:        500,                     // 大幅增加空闲连接数
		MaxIdleConnsPerHost: 100,                     // 每主机最多100个空闲连接
		IdleConnTimeout:     60 * time.Second,        // 增加空闲超时保持连接
		DisableKeepAlives:   false,                   // 强制启用连接复用
	}
}

// ProxyVerifier 极致高并发代理验证器
type ProxyVerifier struct {
	config        *ProxyVerifierConfig
	logger        *logrus.Logger
	engine        *Engine
	httpClient    *http.Client
	baseTransport *http.Transport
	clientPool    sync.Pool  // 客户端池
	cache         ProxyCache // 缓存接口
	mu            sync.RWMutex
	stopped       bool
}

// NewProxyVerifier 创建新的极致高并发代理验证器
func NewProxyVerifier(config *ProxyVerifierConfig, logger *logrus.Logger) *ProxyVerifier {
	if config == nil {
		config = DefaultProxyVerifierConfig()
	}

	if logger == nil {
		logger = logrus.New()
	}

	// 创建极致性能的传输层配置
	baseTransport := &http.Transport{
		MaxIdleConns:        1000,            // 大幅增加空闲连接数
		MaxIdleConnsPerHost: 200,             // 每个主机最多200个空闲连接
		IdleConnTimeout:     2 * time.Minute, // 长时间保持连接
		DisableKeepAlives:   false,           // 强制启用连接复用
		DialContext: (&net.Dialer{
			Timeout:   config.ConnectTimeout,
			KeepAlive: 2 * time.Minute, // 长时间保持活跃连接
			DualStack: true,            // 启用IPv4和IPv6双栈
		}).DialContext,
		TLSHandshakeTimeout:   3 * time.Second, // 减少TLS握手超时
		ResponseHeaderTimeout: config.VerificationTimeout,
		ExpectContinueTimeout: 500 * time.Millisecond, // 减少等待时间
		DisableCompression:    true,                   // 禁用压缩减少CPU开销
		MaxConnsPerHost:       300,                    // 大幅增加每主机连接数
		// 激进的连接管理
		WriteBufferSize: 64 * 1024, // 64KB写缓冲
		ReadBufferSize:  64 * 1024, // 64KB读缓冲
	}

	// 创建基础HTTP客户端（不带代理）
	httpClient := &http.Client{
		Transport: baseTransport,
		Timeout:   config.VerificationTimeout,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			return http.ErrUseLastResponse // 不跟随重定向
		},
	}

	pv := &ProxyVerifier{
		config:        config,
		logger:        logger,
		httpClient:    httpClient,
		baseTransport: baseTransport,
		stopped:       false,
	}

	// 初始化客户端池
	pv.clientPool = sync.Pool{
		New: func() interface{} {
			return pv.createPooledClient()
		},
	}

	// 启动连接清理器
	go pv.startConnectionCleaner()

	// 创建引擎配置
	engineConfig := &Config{
		MaxWorkers:       config.MaxWorkers,
		QueueSize:        config.QueueSize,
		ResultBufferSize: config.ResultBufferSize,
		WorkerPoolSize:   config.MaxWorkers / 2,
		BatchSize:        config.BatchSize,
		FlushInterval:    50 * time.Millisecond,
		TaskTimeout:      config.VerificationTimeout,
		RetryAttempts:    config.RetryAttempts,
		RetryDelay:       config.RetryDelay,
		ProgressInterval: config.ProgressInterval,
		EnableMetrics:    config.EnableMetrics,
		AutoScale:        config.EnableAutoScale,
		ScaleThreshold:   0.7, // 70%负载时扩容
		MaxScaleWorkers:  config.MaxScaleWorkers,
	}

	// 创建引擎
	pv.engine = NewEngine(engineConfig, logger, pv.verifyProxyWorker)

	return pv
}

// createPooledClient 创建池化的HTTP客户端
func (pv *ProxyVerifier) createPooledClient() *http.Client {
	// 克隆基础传输层
	transport := pv.baseTransport.Clone()

	return &http.Client{
		Transport: transport,
		Timeout:   pv.config.VerificationTimeout,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			return http.ErrUseLastResponse
		},
	}
}

// getClient 从池中获取客户端
func (pv *ProxyVerifier) getClient(proxyURL *url.URL) *http.Client {
	client := pv.clientPool.Get().(*http.Client)

	// 设置代理
	if transport, ok := client.Transport.(*http.Transport); ok {
		transport.Proxy = http.ProxyURL(proxyURL)
	}

	return client
}

// putClient 将客户端放回池中
func (pv *ProxyVerifier) putClient(client *http.Client) {
	// 清理代理设置但保持连接复用
	if transport, ok := client.Transport.(*http.Transport); ok {
		transport.Proxy = nil
		// 不立即关闭连接，保持连接池效率
		// transport.CloseIdleConnections() // 移除此行以保持连接复用
	}

	pv.clientPool.Put(client)
}

// Stop 停止验证器并清理资源
func (pv *ProxyVerifier) Stop() {
	pv.mu.Lock()
	defer pv.mu.Unlock()

	if pv.stopped {
		return
	}

	pv.stopped = true

	// 停止引擎
	if pv.engine != nil {
		pv.engine.Stop()
	}

	// 关闭基础传输层的空闲连接
	if pv.baseTransport != nil {
		pv.baseTransport.CloseIdleConnections()
	}

	pv.logger.Info("ProxyVerifier stopped and resources cleaned")
}

// SetCache 设置缓存接口
func (pv *ProxyVerifier) SetCache(cache ProxyCache) {
	pv.mu.Lock()
	defer pv.mu.Unlock()
	pv.cache = cache
}

// startConnectionCleaner 启动优化的连接清理器
func (pv *ProxyVerifier) startConnectionCleaner() {
	ticker := time.NewTicker(2 * time.Minute) // 从30秒增加到2分钟
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			pv.mu.RLock()
			if pv.stopped {
				pv.mu.RUnlock()
				return
			}
			pv.mu.RUnlock()

			// 清理基础传输层的空闲连接（适度清理）
			if pv.baseTransport != nil {
				pv.baseTransport.CloseIdleConnections()
			}

			// 减少垃圾回收频率以提升性能
			// runtime.GC() // 移除强制GC，让系统自动管理

			pv.logger.Debug("Connection cleaner: idle connections closed")
		}
	}
}

// 极致高并发验证代理列表
func (pv *ProxyVerifier) VerifyProxies(ctx context.Context, proxies []ProxyData, progressCallback func(completed, total, successful, failed int64)) ([]*ProxyResult, error) {
	if len(proxies) == 0 {
		return nil, nil
	}

	pv.logger.WithFields(logrus.Fields{
		"total_proxies":     len(proxies),
		"max_workers":       pv.config.MaxWorkers,
		"max_scale_workers": pv.config.MaxScaleWorkers,
		"test_url":          pv.config.TestURL,
		"timeout":           pv.config.VerificationTimeout,
		"expected_speed":    fmt.Sprintf("%.0f proxies/sec", float64(pv.config.MaxWorkers)*5),
	}).Info("Starting ultra-concurrent proxy verification")

	// 转换为interface{}切片
	data := make([]interface{}, len(proxies))
	for i, proxy := range proxies {
		// 设置默认测试URL
		if proxy.TestURL == "" {
			proxy.TestURL = pv.config.TestURL
		}
		if proxy.Timeout == 0 {
			proxy.Timeout = pv.config.VerificationTimeout
		}
		if proxy.ConnectTimeout == 0 {
			proxy.ConnectTimeout = pv.config.ConnectTimeout
		}
		data[i] = proxy
	}

	// 执行极致高并发验证
	results, err := pv.engine.Execute(ctx, data, progressCallback)
	if err != nil {
		return nil, fmt.Errorf("ultra-concurrent verification failed: %w", err)
	}

	// 转换结果
	proxyResults := make([]*ProxyResult, len(results))
	for i, result := range results {
		if result != nil && result.TaskID < len(proxies) {
			originalProxy := proxies[result.TaskID]
			proxyResults[i] = &ProxyResult{
				Host:         originalProxy.Host,
				Port:         originalProxy.Port,
				Success:      result.Success,
				Error:        result.Error,
				ResponseTime: result.Elapsed,
			}
		} else if i < len(proxies) {
			// 如果结果为空，创建一个失败结果
			originalProxy := proxies[i]
			proxyResults[i] = &ProxyResult{
				Host:    originalProxy.Host,
				Port:    originalProxy.Port,
				Success: false,
				Error:   "verification failed",
			}
		}
	}

	// 统计结果
	successful := 0
	failed := 0
	for _, result := range proxyResults {
		if result != nil {
			if result.Success {
				successful++
			} else {
				failed++
			}
		}
	}

	pv.logger.WithFields(logrus.Fields{
		"total_verified": len(proxyResults),
		"successful":     successful,
		"failed":         failed,
		"success_rate":   fmt.Sprintf("%.2f%%", float64(successful)/float64(len(proxyResults))*100),
	}).Info("Ultra-concurrent proxy verification completed")

	return proxyResults, nil
}

// verifyProxyWorker 高性能代理验证工作函数（极简版）
func (pv *ProxyVerifier) verifyProxyWorker(ctx context.Context, data interface{}) (bool, error) {
	proxyData, ok := data.(ProxyData)
	if !ok {
		return false, fmt.Errorf("invalid proxy data type")
	}

	// 构建代理URL
	proxyURL, err := pv.buildProxyURL(proxyData)
	if err != nil {
		return false, err
	}

	// 从池中获取客户端
	client := pv.getClient(proxyURL)
	defer pv.putClient(client)

	// 极短超时设置
	client.Timeout = proxyData.Timeout

	// 使用HEAD请求代替GET，减少数据传输
	req, err := http.NewRequestWithContext(ctx, "HEAD", proxyData.TestURL, nil)
	if err != nil {
		return false, err
	}

	// 最小化请求头
	req.Header.Set("User-Agent", utils.RandUserAgent())

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		// 验证失败，设置缓存
		pv.setCacheIfAvailable(proxyData.Host, proxyData.Port, false)
		return false, err
	}
	defer resp.Body.Close()

	// 简单成功判断：只要返回2xx状态码就认为代理可用
	success := resp.StatusCode >= 200 && resp.StatusCode < 300

	// 设置缓存结果
	pv.setCacheIfAvailable(proxyData.Host, proxyData.Port, success)

	return success, nil
}

// setCacheIfAvailable 如果缓存可用，则设置缓存
func (pv *ProxyVerifier) setCacheIfAvailable(host string, port int, isValid bool) {
	pv.mu.RLock()
	cache := pv.cache
	pv.mu.RUnlock() // 立即释放锁，避免死锁

	if cache != nil {
		cache.Set(host, port, isValid)
	}
}

// buildProxyURL 构建代理URL
func (pv *ProxyVerifier) buildProxyURL(proxy ProxyData) (*url.URL, error) {
	var scheme string
	switch proxy.Type {
	case "http", "https":
		scheme = "http"
	case "socks4":
		scheme = "socks4"
	case "socks5":
		scheme = "socks5"
	default:
		scheme = "http"
	}

	// 构建URL
	proxyURL := &url.URL{
		Scheme: scheme,
		Host:   fmt.Sprintf("%s:%d", proxy.Host, proxy.Port),
	}

	// 添加认证信息
	if proxy.Username != "" {
		if proxy.Password != "" {
			proxyURL.User = url.UserPassword(proxy.Username, proxy.Password)
		} else {
			proxyURL.User = url.User(proxy.Username)
		}
	}

	return proxyURL, nil
}

// GetStats 获取验证统计信息
func (pv *ProxyVerifier) GetStats() (completed, total, successful, failed int64, elapsed time.Duration) {
	return pv.engine.GetStats()
}

// StopEngine 停止引擎
func (pv *ProxyVerifier) StopEngine() error {
	return pv.engine.Stop()
}
