package routes

import (
	"net/http"
	"proxyFlow/api/handlers"
	"proxyFlow/internal/middleware"
	"proxyFlow/internal/models"
	"proxyFlow/internal/proxy"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/sirupsen/logrus"
)

// SetupRoutes 设置路由
func SetupRoutes(
	router *gin.Engine,
	authHandler *handlers.AuthHandler,
	proxyHandler *handlers.ProxyHandler,
	taskHandler *handlers.TaskHandler,
	apiKeyHandler *handlers.APIKeyHandler,
	settingsHandler *handlers.SettingsHandler,
	collectorHandler *handlers.CollectorHandler,
	authMiddleware *middleware.AuthMiddleware,
	proxyManager *proxy.Manager,
	logger *logrus.Logger,
) {
	// 健康检查
	router.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{"status": "ok"})
	})

	// Prometheus指标
	router.GET("/metrics", gin.WrapH(promhttp.Handler()))

	// 创建标签处理器
	proxyTagHandler := handlers.NewProxyTagHandler(proxyManager, logger)

	// API v1
	v1 := router.Group("/api/v1")
	{
		// 认证路由
		auth := v1.Group("/auth")
		{
			auth.POST("/register", authHandler.Register)
			auth.POST("/login", authHandler.Login)
			auth.GET("/profile", authMiddleware.Authenticate(), authHandler.GetProfile)
		}

		// 用户管理路由
		user := v1.Group("/user")
		{
			user.Use(authMiddleware.Authenticate())

			// 新版本多API Key 管理
			apikeys := user.Group("/apikeys")
			{
				apikeys.GET("", apiKeyHandler.GetAPIKeys)          // 获取API密钥列表
				apikeys.POST("", apiKeyHandler.CreateAPIKey)       // 创建新的API密钥
				apikeys.PUT("/:id", apiKeyHandler.UpdateAPIKey)    // 更新API密钥
				apikeys.DELETE("/:id", apiKeyHandler.DeleteAPIKey) // 删除API密钥
			}

			// 用户设置管理
			user.GET("/settings", settingsHandler.GetUserSettings)      // 获取用户设置
			user.PUT("/settings", settingsHandler.UpdateUserSettings)   // 更新用户设置
			user.POST("/settings/reset", settingsHandler.ResetSettings) // 重置设置

			// 代理设置
			user.GET("/proxy-settings", settingsHandler.GetProxySettings)    // 获取代理设置
			user.PUT("/proxy-settings", settingsHandler.UpdateProxySettings) // 更新代理设置
		}

		// 代理路由
		proxies := v1.Group("/proxies")
		{
			// 地理位置相关路由 - 放在前面避免与/:id冲突
			proxies.GET("/location/stats", authMiddleware.Authenticate(), proxyHandler.GetProxyLocationStats)
			proxies.GET("/location/groups", authMiddleware.Authenticate(), proxyHandler.GroupProxiesByLocation)
			proxies.GET("/location", authMiddleware.Authenticate(), proxyHandler.GetProxiesByLocation)
			// 质量评估相关路由
			proxies.GET("/quality/top", authMiddleware.Authenticate(), proxyHandler.GetTopQualityProxies)
			proxies.GET("/quality", authMiddleware.Authenticate(), proxyHandler.GetProxiesByQuality)
			proxies.POST("/quality/assess", authMiddleware.Authenticate(), authMiddleware.RequireRole(models.UserRoleAdmin), proxyHandler.BatchAssessQuality)
			// 标签和智能路由相关路由
			proxies.GET("/tags", authMiddleware.Authenticate(), proxyHandler.GetProxiesByTags)
			proxies.GET("/scenario", authMiddleware.Authenticate(), proxyHandler.GetProxiesByScenario)
			proxies.GET("/smart-routing", authMiddleware.Authenticate(), proxyHandler.GetProxyWithSmartRouting)
			// 其他具体路径
			proxies.GET("/available", authMiddleware.Authenticate(), proxyHandler.GetAvailableProxies)
			proxies.GET("/stats", authMiddleware.Authenticate(), proxyHandler.GetProxyStats)
			proxies.GET("/strategy", authMiddleware.Authenticate(), proxyHandler.GetProxyByStrategy)
			// 基本CRUD操作
			proxies.GET("", authMiddleware.Authenticate(), proxyHandler.GetProxies)
			proxies.POST("", authMiddleware.Authenticate(), authMiddleware.RequireRole(models.UserRoleAdmin), proxyHandler.AddProxy)
			// 批量操作
			proxies.POST("/batch/health-check", authMiddleware.Authenticate(), proxyHandler.BatchHealthCheck)
			proxies.POST("/health-check-all", authMiddleware.Authenticate(), proxyHandler.HealthCheckAll)
			// super admin 批量导入代理
			proxies.POST("/batch-import", authMiddleware.Authenticate(), authMiddleware.RequireRole(models.UserRoleAdmin), proxyHandler.BatchImportProxies)
			// 带ID的路由放在最后
			proxies.GET("/:id", authMiddleware.Authenticate(), proxyHandler.GetProxy)
			proxies.DELETE("/:id", authMiddleware.Authenticate(), authMiddleware.RequireRole(models.UserRoleAdmin), proxyHandler.DeleteProxy)
			proxies.POST("/:id/health-check", authMiddleware.Authenticate(), proxyHandler.HealthCheck)
			proxies.POST("/:id/quality/assess", authMiddleware.Authenticate(), authMiddleware.RequireRole(models.UserRoleAdmin), proxyHandler.AssessProxyQuality)
			// 代理标签管理
			// proxies.GET("/:proxy_id/tags", authMiddleware.Authenticate(), proxyTagHandler.GetProxyTags)
			// proxies.DELETE("/:proxy_id/tags", authMiddleware.Authenticate(), authMiddleware.RequireRole(models.UserRoleAdmin), proxyTagHandler.RemoveTags)
		}

		// 标签管理路由
		tags := v1.Group("/tags")
		{
			tags.Use(authMiddleware.Authenticate())
			tags.GET("", proxyTagHandler.GetTags)
			tags.POST("", authMiddleware.RequireRole(models.UserRoleAdmin), proxyTagHandler.CreateTag)
			tags.GET("/:id", proxyTagHandler.GetTag)
			tags.PUT("/:id", authMiddleware.RequireRole(models.UserRoleAdmin), proxyTagHandler.UpdateTag)
			tags.DELETE("/:id", authMiddleware.RequireRole(models.UserRoleAdmin), proxyTagHandler.DeleteTag)
			tags.POST("/assign", authMiddleware.RequireRole(models.UserRoleAdmin), proxyTagHandler.AssignTags)
		}

		// 任务路由
		tasks := v1.Group("/tasks")
		{
			tasks.Use(authMiddleware.Authenticate())
			tasks.GET("", taskHandler.GetUserTasks)
			tasks.GET("/stats", taskHandler.GetTaskStats)
			tasks.POST("", taskHandler.CreateTask)
			tasks.GET("/:id", taskHandler.GetTask)
			tasks.PUT("/:id", taskHandler.UpdateTask)
			tasks.DELETE("/:id", taskHandler.DeleteTask)
			tasks.PATCH("/:id/cancel", taskHandler.CancelTask)
			// 批量操作
			tasks.POST("/batch-delete", taskHandler.BatchDeleteTasks)
			tasks.POST("/batch-cancel", taskHandler.BatchCancelTasks)
		}

		// 采集器路由
		collector := v1.Group("/collector")
		{
			collector.Use(authMiddleware.Authenticate())
			collector.GET("/status", collectorHandler.GetStatus)
			collector.GET("/stats", collectorHandler.GetStats)
			collector.GET("/metrics", collectorHandler.GetMetrics)
			collector.GET("/history", collectorHandler.GetHistory)

			// 任务管理路由
			tasks := collector.Group("/tasks")
			{
				tasks.GET("", collectorHandler.ListTasks)
				tasks.POST("", authMiddleware.RequireRole(models.UserRoleAdmin), collectorHandler.StartTask)
				tasks.GET("/:taskId", collectorHandler.GetTask)
				tasks.POST("/:taskId/pause", authMiddleware.RequireRole(models.UserRoleAdmin), collectorHandler.PauseTask)
				tasks.POST("/:taskId/resume", authMiddleware.RequireRole(models.UserRoleAdmin), collectorHandler.ResumeTask)
				tasks.POST("/:taskId/cancel", authMiddleware.RequireRole(models.UserRoleAdmin), collectorHandler.CancelTask)
				tasks.GET("/:taskId/progress", collectorHandler.GetProgress)
			}

			// 队列管理路由
			queue := collector.Group("/queue")
			{
				queue.GET("/status", collectorHandler.GetQueueStatus)
			}

			// 管理员操作
			collector.POST("/collect", authMiddleware.RequireRole(models.UserRoleAdmin), collectorHandler.TriggerCollection)
			collector.POST("/start", authMiddleware.RequireRole(models.UserRoleAdmin), collectorHandler.StartCollector)
			collector.POST("/stop", authMiddleware.RequireRole(models.UserRoleAdmin), collectorHandler.StopCollector)
			// 独立采集任务（推荐用于大规模采集）
			collector.POST("/standalone", authMiddleware.RequireRole(models.UserRoleAdmin), collectorHandler.StartStandaloneCollection)
		}
	}

	// 静态文件服务 - 为前端提供服务
	router.Static("/static", "./web/build")
	router.StaticFile("/favicon.ico", "./web/build/favicon.ico")
	router.StaticFile("/manifest.json", "./web/build/manifest.json")

	// SPA路由处理 - 所有未匹配的路由都返回index.html
	router.NoRoute(func(c *gin.Context) {
		// 如果是API请求，返回404
		if len(c.Request.URL.Path) >= 4 && c.Request.URL.Path[:4] == "/api" {
			c.JSON(http.StatusNotFound, gin.H{"error": "API endpoint not found"})
			return
		}
		// js css 等资源正常不用管，Static 已经处理了
		_p := c.Request.URL.Path
		if strings.Contains(_p, "js") || strings.Contains(_p, "css") || strings.Contains(_p, "ico") || strings.Contains(_p, "json") {
			return
		}
		// 否则返回前端应用
		c.File("./web/build/index.html")
	})
}
