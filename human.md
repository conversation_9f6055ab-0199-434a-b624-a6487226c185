请根据当前项目情况，阅读项目源码并完成以下目标。
在 @freeProxy/sources.json 文件有代理源信息，你可以阅读该文件。然后帮我实现以下目标：
1. 代理源包含了各种类型的代理，你应该对每个代理源发起请求，获取实际的代理，然后根据实际情况存储到某个文件中。
2. 有相应的检测逻辑，检测获取到的代理是否可用，延迟信息之类的。
3. 将检测到可用的代理添加到本项目的后端服务中。
4. 这个逻辑最好做成一个独立的模块，独立的文件夹之类的。
5. 这个服务应该不影响程序主流程运行，或许可以用一个goroutine。
6. 增加你认为应该增加的逻辑。

------------------------------------------------------------------------------------
文件 @b:\conan-work\proxyFlow/internal\collector\verifier.go 有个验证代理是否有效的逻辑：verifyProxies 但是验证的代理数量可能超过10万条，如何才能做到更加高效的验证，并且能有合适的进度提示。最好还有个本地文件能简单记录上次验证的时间，如果没有超过24小时则不用重复验证。


------------------------------------------------------------------------------------
我觉得目前的方案可能稍微有点复杂了，我理解这个需求代码量不应该有这么多。
应该在不影响原有功能的前提增加新功能。请帮我精简代码，实现刚刚的需求。最后不需要测试文件，只需要通过go run cmd/main.go 来验证。


------------------------------------------------------------------------------------
当前项目是一个基于 golang 的代理池项目，有一个 collector 收集器功能，能收集并且检测代理有效性能。具体逻辑位于： @/Users/<USER>/Desktop/conan-work/proxyManager/internal/collector/ 。
请帮我阅读源码，然后调研 golang 有什么比较好用的命令行显示方案，我需要人性化的显示 collector  verifyProxies 检验代理是否有效的过程，需要动态更新显示检测进度，预估时间等。
请帮我选择最成熟高效的方案，然后对代码进行修改


------------------------------------------------------------------------------------
项目入口位于： @/Users/<USER>/Desktop/conan-work/proxyManager/cmd/main.go  请帮我阅读相关源码，检查运行流程，目前的“检测代理”运行流程并没有看到刚刚的修改。


------------------------------------------------------------------------------------
请帮我阅读分析 @internal/app/application.go 中启动代理采集器的逻辑以及运行流程.
目前采集器和其他的BackgroundServices都是程序开始的时候后端异步运行,由于代理采集器需要检验接近200万代理的可连接性.
请帮我综合分析,这部分功能是否需要单独拆分出来?如果需要,那么如何拆分?拆分后如何调用?
请进行调研,修改过程中尽量考虑全面,细致.
提供一个思路: 将采集器的功能拆分到一个独立的模块中,然后可以直接通过 go run xxx 运行这个模块,或者/xxx 路由调用运行,然后把采集到的数据写入数据库, 这样是否就避免了耦合,也更加合理
修改进尽量简洁，不要过度涉及. 修改功能最好不要影响到其他功能. 确保程序能正确编译.



------------------------------------------------------------------------------------



------------------------------------------------------------------------------------




------------------------------------------------------------------------------------



------------------------------------------------------------------------------------




------------------------------------------------------------------------------------


